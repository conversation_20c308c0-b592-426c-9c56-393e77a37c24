# Configuration Google OAuth pour Supabase

## 1. Configuration dans Supabase

### Étape 1: Accéder aux paramètres d'authentification
1. <PERSON><PERSON> sur https://supabase.com/dashboard
2. Sélectionner votre projet
3. Aller dans **Authentication** > **Providers**
4. Trouver **Google** dans la liste

### Étape 2: Configurer Google OAuth
1. Activer le toggle **Enable sign in with Google**
2. Ajouter les informations suivantes :
   - **Client ID**: `VOTRE_GOOGLE_CLIENT_ID`
   - **Client Secret**: `VOTRE_GOOGLE_CLIENT_SECRET`

### Étape 3: Configurer les URLs de redirection
Dans la section **Redirect URLs**, ajouter :
- `http://localhost:3000/auth/callback` (pour le développement)
- `https://votre-domaine.com/auth/callback` (pour la production)

## 2. Configuration dans Google Cloud Console

### Étape 1: Vérifier les URLs autorisées
1. <PERSON><PERSON> sur https://console.cloud.google.com/
2. Sélectionner votre projet
3. Aller dans **APIs & Services** > **Credentials**
4. Cliquer sur votre OAuth 2.0 Client ID

### Étape 2: Ajouter les URLs de redirection autorisées
Dans **Authorized redirect URIs**, ajouter :
- `https://VOTRE_PROJET_ID.supabase.co/auth/v1/callback`
- `http://localhost:3001/auth/callback`

### Étape 3: Ajouter les origines JavaScript autorisées
Dans **Authorized JavaScript origins**, ajouter :
- `http://localhost:3001`
- `https://votre-domaine.com` (pour la production)

## 3. Test de la configuration

### Vérifications à faire :
1. ✅ Les clés Google sont dans `.env.local`
2. ✅ Google OAuth est activé dans Supabase
3. ✅ Les URLs de redirection sont configurées
4. ✅ L'application peut se connecter à Supabase

### Test de connexion :
1. Lancer l'application : `npm run dev`
2. Aller sur http://localhost:3000/auth/login
3. Cliquer sur "Continuer avec Google"
4. Vérifier que la redirection fonctionne

## 4. Résolution des problèmes courants

### Erreur "redirect_uri_mismatch"
- Vérifier que l'URL de redirection dans Google Cloud Console correspond exactement
- Format attendu : `https://VOTRE_PROJET_ID.supabase.co/auth/v1/callback`

### Erreur "Table 'public.profiles' does not exist"
- Exécuter le script SQL dans l'éditeur Supabase
- Ou laisser l'application créer automatiquement les tables

### Erreur "Invalid client"
- Vérifier que le Client ID et Client Secret sont corrects
- Vérifier que l'OAuth consent screen est configuré

## 5. Script SQL à exécuter dans Supabase

```sql
-- Exécuter ce script dans l'éditeur SQL de Supabase
-- pour créer les tables nécessaires

-- Table des profils utilisateurs
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  risk_tolerance TEXT CHECK (risk_tolerance IN ('conservative', 'moderate', 'aggressive')) DEFAULT 'moderate',
  investment_horizon TEXT CHECK (investment_horizon IN ('short', 'medium', 'long')) DEFAULT 'medium',
  monthly_budget DECIMAL(10,2) DEFAULT 1000,
  sharia_purity_level INTEGER CHECK (sharia_purity_level >= 95 AND sharia_purity_level <= 100) DEFAULT 98,
  boycott_preferences TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fonction pour créer automatiquement un profil
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger pour créer le profil automatiquement
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Politiques RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);
```

## 6. Variables d'environnement requises

Vérifier que `.env.local` contient :
```
NEXT_PUBLIC_SUPABASE_URL=https://VOTRE_PROJET_ID.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=VOTRE_SUPABASE_ANON_KEY
NEXT_PUBLIC_GOOGLE_CLIENT_ID=VOTRE_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=VOTRE_GOOGLE_CLIENT_SECRET
```
