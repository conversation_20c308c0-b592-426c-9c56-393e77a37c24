-- <PERSON><PERSON><PERSON> pour corriger les erreurs 406 (Not Acceptable)
-- À exécuter dans Supabase Dashboard → SQL Editor

-- 1. DÉSACTIVER COMPLÈTEMENT RLS pour tester
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. <PERSON><PERSON><PERSON><PERSON> toutes les anciennes politiques
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on id" ON profiles;

-- 3. Accorder des permissions directes à la table
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON profiles TO anon;

-- 4. C<PERSON>er le profil manuellement pour votre utilisateur
INSERT INTO profiles (id, email, full_name, risk_tolerance, investment_horizon, monthly_budget, sharia_purity_level, boycott_preferences)
VALUES ('03ef09c2-dc7f-4c10-b247-97ce2c825a87', '<EMAIL>', 'Votre Nom', 'moderate', 'medium', 1000, 98, '{}')
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = NOW();

-- 5. Vérifier que le profil a été créé
SELECT * FROM profiles WHERE id = '03ef09c2-dc7f-4c10-b247-97ce2c825a87';

-- 6. OPTIONNEL: Réactiver RLS avec des politiques très permissives
-- ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Allow all for authenticated users" ON profiles USING (auth.role() = 'authenticated');

-- 7. Test final
SELECT COUNT(*) as total_profiles FROM profiles;

-- 5. Vérifier que la fonction handle_new_user existe
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name')
  ON CONFLICT (id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Recréer le trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 7. Vérifier les politiques créées
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'profiles';

-- 8. Test : créer un profil manuellement si nécessaire
-- Remplacez USER_ID par votre vrai ID utilisateur
-- INSERT INTO profiles (id, email, full_name, risk_tolerance, investment_horizon, monthly_budget, sharia_purity_level, boycott_preferences)
-- VALUES ('03ef09c2-dc7f-4c10-b247-97ce2c825a87', '<EMAIL>', 'Votre Nom', 'moderate', 'medium', 1000, 98, '{}')
-- ON CONFLICT (id) DO NOTHING;
