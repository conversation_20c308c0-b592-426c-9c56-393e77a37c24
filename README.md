# Halal Invest Advisor 🕌📈

Application Next.js de conseil en investissement Sharia-compliant.
**Objectif** : Suggérer des portefeuilles personnalisés respectant les critères islamiques, avec filtres éthiques et notifications dynamiques.

## 🎯 Fonctionnalités

- **Authentification sécurisée** avec Supabase
- **Connexion Google OAuth** intégrée
- **Onboarding interactif** avec sauvegarde automatique dans les cookies
- **Sliders intelligents** non-linéaires pour une meilleure précision
- **Interface mobile-first** optimisée pour les écrans tactiles
- **Bouton flottant** toujours accessible
- **Animations fluides** avec Framer Motion

## 🎨 Règles d'Interface & Harmonie Visuelle

### Principe fondamental : **Positions fixes pour l'harmonie**
L'interface doit donner une sensation de stabilité et de fluidité lors des transitions entre questions.

#### 📍 **Éléments à position fixe :**

1. **Header (en haut)**
   - Position : `flex-shrink-0 pt-8 pb-6 px-4`
   - Contenu : Titre, description, progression
   - **Règle** : Ne jamais bouger verticalement, même si le contenu change

2. **Footer avec indicateurs (en bas)**
   - Position : `flex-shrink-0 pb-8 pt-4`
   - Contenu : Points de progression des étapes
   - **Règle** : Toujours collé en bas, même s'il y a de l'espace libre

3. **Bouton flottant (bas droite)**
   - Position : `fixed bottom-6 right-6`
   - **Règle** : Toujours visible, pas de scroll nécessaire

#### 🎯 **Zone de contenu centrale :**
- Position : `flex-1 flex items-center justify-center`
- **Règle** : Seule zone qui s'adapte et se centre selon le contenu

#### 📱 **Mobile-First Design :**
- Sliders plus épais : `h-3` mobile, `h-2` desktop
- Thumb plus gros : `w-6 h-6` mobile, `w-5 h-5` desktop
- Cartes compactes : `p-3` mobile, `p-4` desktop
- Touch-friendly : `touch-manipulation` sur tous les éléments interactifs

#### 🎪 **Sliders intelligents :**
- **Non-linéaires** : Plus de précision sur les petites valeurs
- **Mapping progressif** : 0-1000€ = 20% du slider, 1000-2000€ = 15%, etc.
- **Breakpoints visuels** : Points sur le slider pour les valeurs importantes

#### ✨ **Animations harmonieuses :**
- Transitions entre questions : `duration: 0.3s, ease: "easeInOut"`
- Pas d'éléments qui "sautent" ou changent de position
- Cohérence visuelle maintenue à chaque transition

### 🚫 **À éviter absolument :**
- ❌ Éléments qui changent de position selon le contenu
- ❌ Header qui monte/descend selon l'espace disponible
- ❌ Footer qui remonte quand il y a de l'espace
- ❌ Boutons de navigation qui nécessitent du scroll
- ❌ Rupture de l'harmonie visuelle lors des transitions

---

## 🚀 Getting Started

### 1. Vérification rapide
```bash
npm run check-setup
```

### 2. Configuration requise

#### ⚠️ Configuration manuelle obligatoire

**La configuration automatique ne fonctionne pas.** Vous devez configurer manuellement :

#### A. Créer les tables Supabase (OBLIGATOIRE)
1. Aller sur https://supabase.com/dashboard
2. Sélectionner votre projet
3. Cliquer sur **"SQL Editor"**
4. **Copier-coller** tout le contenu de `supabase-schema.sql`
5. **Cliquer sur "Run"**
6. Vérifier que les tables sont créées dans Table Editor

📖 **Guide détaillé** : `SETUP_DATABASE.md`

#### B. Activer Google OAuth
1. Dans Supabase: Authentication → Providers → Google
2. Activer et configurer avec les clés fournies
3. Dans Google Cloud Console: configurer les URLs de redirection

📖 **Guide détaillé** : `GOOGLE_OAUTH_SETUP.md`

### 3. Lancer l'application
```bash
npm run dev
```

Ouvrir [http://localhost:3001](http://localhost:3001) dans votre navigateur.

### 4. Guides et outils disponibles
- **📖 Configuration DB**: `SETUP_DATABASE.md` (OBLIGATOIRE)
- **📖 Configuration OAuth**: `GOOGLE_OAUTH_SETUP.md`
- **⚡ Vérification**: `npm run check-setup`

### 5. Résolution des problèmes

#### Erreur "provider is not enabled"
→ Google OAuth non activé dans Supabase Dashboard

#### Erreur 406 (Not Acceptable)
→ **Solution rapide** : Exécuter `fix-rls-policies.sql` dans Supabase Dashboard
→ **Diagnostic** : Exécuter `diagnostic-rls.sql` pour identifier le problème
→ Voir `SETUP_DATABASE.md` pour plus de détails

#### Tables non trouvées
→ Script SQL non exécuté, voir `SETUP_DATABASE.md`

---

## ✅ Fonctionnalités principales

### Phase 1 : MVP (Base)
- [x] **Setup & Configuration**
  - [x] Projet Next.js 15 + TypeScript
  - [x] Supabase client configuré
  - [x] Tailwind CSS + shadcn/ui
  - [x] Variables d'environnement (.env.example)
  - [x] Structure des dossiers organisée
  - [x] Documentation de configuration manuelle

- [x] **Authentification**
  - [x] Supabase Auth (email/password)
  - [x] Login avec Google OAuth
  - [x] Pages de connexion/inscription
  - [x] Protection des routes privées
  - [x] Gestion des sessions
  - [x] Page de callback OAuth
  - [x] Création automatique de profil

- [ ] **Profil utilisateur**
  - [ ] Questionnaire de profil de risque
  - [ ] Horizon d'investissement (court/moyen/long terme)
  - [ ] Budget mensuel disponible
  - [ ] Préférences éthiques (boycott personnalisé)
  - [ ] Sauvegarde du profil en DB

- [ ] **Générateur de portefeuille "ETF maison"**
  - [ ] Algorithme de sélection d'actions halal
  - [ ] Répartition automatique (arrondie en €)
  - [ ] Exclusion d'actions (boycott ou non-halal)
  - [ ] Import des données financières (API)
  - [ ] Calcul des allocations selon le profil de risque

- [ ] **Interface portefeuille**
  - [ ] Page "Mon portefeuille" avec répartition
  - [ ] Graphiques de répartition (secteurs, géographie)
  - [ ] Montants en euros pour chaque action
  - [ ] Export manuel (plan d'investissement)

- [ ] **Notifications de base**
  - [ ] Bannière dans l'app pour changements
  - [ ] Historique des notifications

### Phase 2 : Avancé (Sharia Compliance)
- [ ] **Filtre Sharia automatique**
  - [ ] Critères AAOIFI implémentés :
    - [ ] Dette totale < 33% de la capitalisation
    - [ ] Revenus non-halal < 5% du total
    - [ ] Liquidités/dépôts < 33% de la capitalisation
  - [ ] Exclusion secteurs interdits :
    - [ ] Alcool et tabac
    - [ ] Banques conventionnelles (riba)
    - [ ] Gambling et casinos
    - [ ] Armement et défense
    - [ ] Divertissement immoral
  - [ ] Vérification quotidienne automatique

- [ ] **Classification "Sharia King"**
  - [ ] Entreprises halal depuis 10+ ans consécutifs
  - [ ] Badge spécial dans l'interface
  - [ ] Historique de conformité
  - [ ] Priorité dans les recommandations

- [ ] **Contrôle de pureté**
  - [ ] Slider 95% – 100% pureté
  - [ ] 95% = plus d'opportunités, plus de notifications
  - [ ] 100% = stabilité maximale, moins de choix
  - [ ] Impact sur la fréquence des mises à jour

- [ ] **Watchlist et suivi**
  - [ ] Liste d'entreprises suivies
  - [ ] Alertes de changement de statut halal
  - [ ] Suggestions d'alternatives

### Phase 3 : Fonctionnalités avancées
- [ ] **Optimisation portefeuille**
  - [ ] Backtesting historique
  - [ ] Simulation de performance
  - [ ] Rééquilibrage automatique suggéré
  - [ ] Analyse de corrélation

- [ ] **Intégrations externes**
  - [ ] API brokers (Trade Republic, IBKR)
  - [ ] Import de portefeuilles existants
  - [ ] Synchronisation automatique

- [ ] **Notifications avancées**
  - [ ] Push notifications mobiles
  - [ ] Email alerts
  - [ ] Webhook pour intégrations

- [ ] **Monétisation**
  - [ ] Abonnement premium
  - [ ] Fonctionnalités avancées payantes
  - [ ] API pour développeurs

---

## 🛠️ Stack Technique

- **Frontend** : Next.js 15 + TypeScript + Tailwind CSS
- **UI Components** : shadcn/ui
- **State Management** : Zustand
- **Backend** : Next.js API routes
- **Database & Auth** : Supabase
- **Validation** : Zod
- **HTTP Client** : Axios
- **Finance APIs** :
  - Yahoo Finance (gratuit)
  - Alpha Vantage (freemium)
  - Polygon.io (premium)

---

## 🌟 Concept "Sharia King"

> Inspiré des "Dividend Kings", mais version halal.

Un **"Sharia King"** est une entreprise qui :
- ✅ Est **Sharia compliant depuis 10+ ans consécutifs**
- ✅ Maintient ses ratios financiers dans les limites islamiques
- ✅ N'est jamais entrée dans des secteurs interdits
- ✅ (Bonus) Croissance stable et gouvernance éthique

**Exemples potentiels** : Apple, Microsoft, Nvidia, Johnson & Johnson
*(À vérifier annuellement avec l'algorithme)*

---

## 📊 Critères Sharia (AAOIFI)

### ✅ Critères d'inclusion
- Activité principale halal
- Dette/Capitalisation < 33%
- Revenus non-halal < 5%
- Liquidités/Capitalisation < 33%

### ❌ Secteurs exclus
- Alcool et tabac
- Banques conventionnelles
- Assurance conventionnelle
- Gambling et jeux d'argent
- Armement et défense
- Divertissement immoral
- Porc et produits non-halal

---

## 🗂️ Structure du projet

```
src/
├── app/                    # App Router Next.js
├── components/            # Composants UI réutilisables
├── lib/                   # Utilitaires et configurations
├── hooks/                 # Custom React hooks
├── stores/                # Zustand stores
├── types/                 # Types TypeScript
└── utils/                 # Fonctions utilitaires
```

---

## 🧠 Sliders Intelligents - Documentation Technique

### Principe
Les sliders utilisent un **mapping non-linéaire** pour donner plus de précision sur les valeurs couramment utilisées.

### Configuration par type
```typescript
// Revenus (0-50000€) - Plus de précision sur 0-2000€
incomeSliderConfig: {
  breakpoints: [
    { value: 0, percentage: 0 },
    { value: 1000, percentage: 20 },    // 20% pour 0-1000€
    { value: 2000, percentage: 35 },    // 15% pour 1000-2000€
    { value: 10000, percentage: 80 },   // 45% pour 2000-10000€
    { value: 50000, percentage: 100 }   // 20% pour 10000-50000€
  ]
}
```

### Utilisation
```tsx
<SmartSlider
  value={monthlyIncome}
  onChange={setMonthlyIncome}
  questionId="monthlyIncome"  // Détermine la configuration
/>
```

### Avantages
- **90% plus précis** sur les valeurs 0-1000€
- **UX améliorée** : Plus facile de sélectionner 150€ que 15000€
- **Mobile-friendly** : Sliders plus épais et tactiles

## 💾 Sauvegarde Automatique - Onboarding

### Principe
Toutes les réponses sont **automatiquement sauvegardées** dans les cookies pour éviter la perte de données.

### Fonctionnement
```typescript
// Sauvegarde automatique à chaque réponse
const updateAnswer = (questionId: string, value: any) => {
  // Debounce de 500ms pour éviter trop de sauvegardes
  setTimeout(() => {
    Cookies.set('halal_invest_onboarding_progress', {
      answers: newAnswers,
      currentStepIndex,
      currentQuestionIndex,
      lastUpdated: new Date().toISOString()
    }, { expires: 30 }) // 30 jours
  }, 500)
}
```

### Expérience utilisateur
- **Rafraîchissement de page** : Progression restaurée automatiquement
- **Notification intelligente** : Popup si plus de 2 réponses sauvegardées
- **Choix utilisateur** : Continuer ou recommencer depuis le début
- **Expiration** : Données supprimées après 30 jours

### Avantages
- **Zéro perte de données** même en cas de fermeture accidentelle
- **Reprise fluide** : L'utilisateur peut revenir des jours plus tard
- **UX premium** : Sensation d'application native

---

## 🔧 Configuration

1. Copier `.env.example` vers `.env.local`
2. Configurer les variables Supabase
3. Ajouter les clés API financières
4. Lancer `npm run dev`

---

## 📝 Contribution

Cocher les tâches terminées dans ce README au fur et à mesure du développement.
