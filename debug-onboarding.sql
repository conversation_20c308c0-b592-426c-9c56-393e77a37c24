-- Script pour déboguer le problème d'onboarding qui se relance
-- À exécuter dans Supabase Dashboard → SQL Editor

-- 1. Vérifier l'état actuel des profils
SELECT 
  id,
  email,
  full_name,
  onboarding_completed,
  age,
  profession,
  monthly_income,
  monthly_savings,
  has_invested_before,
  risk_tolerance,
  created_at,
  updated_at
FROM profiles
ORDER BY updated_at DESC;

-- 2. Vérifier si la colonne onboarding_completed existe
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name = 'onboarding_completed';

-- 3. Compter les profils avec onboarding complété
SELECT 
  onboarding_completed,
  COUNT(*) as count
FROM profiles
GROUP BY onboarding_completed;

-- 4. Forcer la mise à jour d'un profil spécifique (remplacer l'ID)
-- UPDATE profiles 
-- SET onboarding_completed = true 
-- WHERE id = '03ef09c2-dc7f-4c10-b247-97ce2c825a87';

-- 5. Vérifier les triggers sur la table profiles
SELECT 
  trigger_name,
  event_manipulation,
  action_timing,
  action_statement
FROM information_schema.triggers
WHERE event_object_table = 'profiles';

-- 6. Vérifier les politiques RLS
SELECT 
  policyname,
  cmd,
  permissive,
  roles,
  qual
FROM pg_policies 
WHERE tablename = 'profiles';

SELECT 'Debug onboarding terminé. Vérifiez les résultats ci-dessus.' as result;
