-- Schema pour Halal Invest Advisor
-- À exécuter dans l'éditeur SQL de Supabase

-- Table des profils utilisateurs
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  risk_tolerance TEXT CHECK (risk_tolerance IN ('conservative', 'moderate', 'aggressive')) DEFAULT 'moderate',
  investment_horizon TEXT CHECK (investment_horizon IN ('short', 'medium', 'long')) DEFAULT 'medium',
  monthly_budget DECIMAL(10,2) DEFAULT 1000,
  sharia_purity_level INTEGER CHECK (sharia_purity_level >= 95 AND sharia_purity_level <= 100) DEFAULT 98,
  boycott_preferences TEXT[] DEFAULT '{}',

  -- Nouvelles colonnes d'onboarding
  has_invested_before BOOLEAN DEFAULT false,
  is_currently_investing BOOLEAN DEFAULT false,
  current_investment_amount DECIMAL(12,2),
  monthly_income DECIMAL(10,2),
  monthly_savings DECIMAL(10,2),
  investment_goals TEXT[] DEFAULT '{}',
  age INTEGER,
  profession TEXT,
  onboarding_completed BOOLEAN DEFAULT false,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des actions
CREATE TABLE stocks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  symbol TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  sector TEXT NOT NULL,
  is_sharia_compliant BOOLEAN DEFAULT false,
  sharia_king_since DATE, -- Date depuis laquelle c'est un Sharia King
  debt_ratio DECIMAL(5,2) DEFAULT 0, -- Pourcentage de dette
  non_halal_revenue_ratio DECIMAL(5,2) DEFAULT 0, -- Pourcentage de revenus non-halal
  liquidity_ratio DECIMAL(5,2) DEFAULT 0, -- Pourcentage de liquidités
  last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des portefeuilles
CREATE TABLE portfolios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL,
  allocations JSONB NOT NULL, -- Array d'objets {symbol, percentage, amount}
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des notifications
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  type TEXT CHECK (type IN ('sharia_compliance_change', 'portfolio_update', 'new_recommendation')) NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stocks_updated_at BEFORE UPDATE ON stocks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_portfolios_updated_at BEFORE UPDATE ON portfolios
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour créer automatiquement un profil lors de l'inscription
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger pour créer le profil automatiquement
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Politiques RLS (Row Level Security)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Supprimer les anciennes politiques si elles existent
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

-- Politique pour les profils : les utilisateurs ne peuvent voir que leur propre profil
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Supprimer les anciennes politiques pour portfolios
DROP POLICY IF EXISTS "Users can view own portfolios" ON portfolios;
DROP POLICY IF EXISTS "Users can insert own portfolios" ON portfolios;
DROP POLICY IF EXISTS "Users can update own portfolios" ON portfolios;
DROP POLICY IF EXISTS "Users can delete own portfolios" ON portfolios;

-- Politique pour les portefeuilles : les utilisateurs ne peuvent voir que leurs propres portefeuilles
CREATE POLICY "Users can view own portfolios" ON portfolios
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own portfolios" ON portfolios
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own portfolios" ON portfolios
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own portfolios" ON portfolios
  FOR DELETE USING (auth.uid() = user_id);

-- Supprimer les anciennes politiques pour notifications
DROP POLICY IF EXISTS "Users can view own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can insert own notifications" ON notifications;

-- Politique pour les notifications : les utilisateurs ne peuvent voir que leurs propres notifications
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notifications" ON notifications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Supprimer les anciennes politiques pour stocks
DROP POLICY IF EXISTS "Stocks are viewable by everyone" ON stocks;

-- Les actions sont publiques en lecture
ALTER TABLE stocks ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Stocks are viewable by everyone" ON stocks
  FOR SELECT USING (true);

-- Index pour améliorer les performances
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_stocks_symbol ON stocks(symbol);
CREATE INDEX idx_stocks_sharia_compliant ON stocks(is_sharia_compliant);
CREATE INDEX idx_stocks_sharia_king ON stocks(sharia_king_since) WHERE sharia_king_since IS NOT NULL;
CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = false;

-- Données d'exemple pour les actions (quelques Sharia Kings potentiels)
INSERT INTO stocks (symbol, name, sector, is_sharia_compliant, sharia_king_since, debt_ratio, non_halal_revenue_ratio, liquidity_ratio) VALUES
('AAPL', 'Apple Inc.', 'Technology', true, '2010-01-01', 15.2, 0.0, 25.8),
('MSFT', 'Microsoft Corporation', 'Technology', true, '2012-01-01', 12.8, 0.0, 28.3),
('GOOGL', 'Alphabet Inc.', 'Technology', true, '2015-01-01', 8.5, 2.1, 32.1),
('NVDA', 'NVIDIA Corporation', 'Technology', true, '2018-01-01', 5.2, 0.0, 18.7),
('TSLA', 'Tesla Inc.', 'Automotive', true, null, 8.9, 0.0, 15.4),
('JNJ', 'Johnson & Johnson', 'Healthcare', true, '2008-01-01', 22.1, 0.0, 12.5),
('PG', 'Procter & Gamble', 'Consumer Goods', true, '2005-01-01', 28.7, 0.0, 8.9),
('UNH', 'UnitedHealth Group', 'Healthcare', true, '2014-01-01', 31.2, 0.0, 6.8),
('HD', 'Home Depot', 'Retail', true, '2011-01-01', 45.8, 0.0, 4.2),
('V', 'Visa Inc.', 'Financial Services', false, null, 18.5, 8.7, 22.1); -- Exclu car services financiers

-- Fonction pour calculer le score Sharia d'une action
CREATE OR REPLACE FUNCTION calculate_sharia_score(
  p_debt_ratio DECIMAL,
  p_non_halal_revenue_ratio DECIMAL,
  p_liquidity_ratio DECIMAL,
  p_sharia_king_since DATE,
  p_sector TEXT
)
RETURNS INTEGER AS $$
DECLARE
  score INTEGER := 100;
  excluded_sectors TEXT[] := ARRAY['Alcohol', 'Tobacco', 'Gambling', 'Conventional Banking', 'Conventional Insurance', 'Weapons & Defense', 'Adult Entertainment', 'Pork Products'];
BEGIN
  -- Pénalités pour les ratios
  IF p_debt_ratio > 33 THEN
    score := score - ((p_debt_ratio - 33) * 2)::INTEGER;
  END IF;
  
  IF p_non_halal_revenue_ratio > 5 THEN
    score := score - ((p_non_halal_revenue_ratio - 5) * 10)::INTEGER;
  END IF;
  
  IF p_liquidity_ratio > 33 THEN
    score := score - ((p_liquidity_ratio - 33) * 2)::INTEGER;
  END IF;
  
  -- Bonus pour les Sharia Kings
  IF p_sharia_king_since IS NOT NULL AND p_sharia_king_since <= (CURRENT_DATE - INTERVAL '10 years') THEN
    score := score + 10;
  END IF;
  
  -- Pénalité majeure pour les secteurs exclus
  IF p_sector = ANY(excluded_sectors) THEN
    score := 0;
  END IF;
  
  RETURN GREATEST(0, LEAST(100, score));
END;
$$ LANGUAGE plpgsql;
