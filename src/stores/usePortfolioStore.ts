import { create } from 'zustand'
import { Portfolio, PortfolioRecommendation, Stock } from '@/types'
import { supabase } from '@/lib/supabase'

interface PortfolioState {
  portfolios: Portfolio[]
  currentRecommendation: PortfolioRecommendation | null
  availableStocks: Stock[]
  loading: boolean
  generateRecommendation: (
    amount: number,
    riskTolerance: string,
    purityLevel: number,
    boycottList: string[]
  ) => Promise<PortfolioRecommendation>
  savePortfolio: (portfolio: Omit<Portfolio, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  fetchPortfolios: (userId: string) => Promise<void>
  fetchAvailableStocks: () => Promise<void>
  updateStockCompliance: () => Promise<void>
}

export const usePortfolioStore = create<PortfolioState>((set, get) => ({
  portfolios: [],
  currentRecommendation: null,
  availableStocks: [],
  loading: false,

  generateRecommendation: async (
    amount: number,
    riskTolerance: string,
    purityLevel: number,
    boycottList: string[]
  ) => {
    set({ loading: true })
    
    try {
      // Récupérer les actions halal disponibles
      const { data: stocks, error } = await supabase
        .from('stocks')
        .select('*')
        .eq('is_sharia_compliant', true)
        .not('symbol', 'in', `(${boycottList.map(s => `"${s}"`).join(',')})`)

      if (error) throw error

      // Algorithme simple de répartition basé sur le profil de risque
      const filteredStocks = stocks.filter(stock => {
        // Filtrer selon le niveau de pureté
        if (purityLevel >= 99) {
          // Très strict : seulement les Sharia Kings
          return stock.sharia_king_since !== null
        } else if (purityLevel >= 97) {
          // Strict : ratios très conservateurs
          return stock.debt_ratio <= 25 && stock.non_halal_revenue_ratio <= 3
        } else {
          // Standard : critères AAOIFI normaux
          return stock.debt_ratio <= 33 && stock.non_halal_revenue_ratio <= 5
        }
      })

      // Répartition selon le profil de risque
      let allocations = []
      
      if (riskTolerance === 'conservative') {
        // Privilégier les Sharia Kings et les grandes caps
        const shariaKings = filteredStocks.filter(s => s.sharia_king_since !== null)
        allocations = distributeConservative(shariaKings.slice(0, 8), amount)
      } else if (riskTolerance === 'moderate') {
        // Mix équilibré
        allocations = distributeModerate(filteredStocks.slice(0, 12), amount)
      } else {
        // Plus de diversification, inclure des small caps
        allocations = distributeAggressive(filteredStocks.slice(0, 15), amount)
      }

      const recommendation: PortfolioRecommendation = {
        allocations,
        totalAmount: amount,
        riskScore: getRiskScore(riskTolerance),
        shariaCompliance: purityLevel,
        expectedReturn: getExpectedReturn(riskTolerance),
        reasoning: generateReasoning(riskTolerance, purityLevel, allocations.length)
      }

      set({ currentRecommendation: recommendation, loading: false })
      return recommendation

    } catch (error) {
      console.error('Error generating recommendation:', error)
      set({ loading: false })
      throw error
    }
  },

  savePortfolio: async (portfolio) => {
    const { data, error } = await supabase
      .from('portfolios')
      .insert({
        user_id: portfolio.userId,
        name: portfolio.name,
        total_amount: portfolio.totalAmount,
        allocations: portfolio.allocations,
      })
      .select()
      .single()

    if (error) throw error

    // Convertir et ajouter à la liste
    const newPortfolio: Portfolio = {
      id: data.id,
      userId: data.user_id,
      name: data.name,
      totalAmount: data.total_amount,
      allocations: data.allocations,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    }

    set(state => ({
      portfolios: [...state.portfolios, newPortfolio]
    }))
  },

  fetchPortfolios: async (userId: string) => {
    const { data, error } = await supabase
      .from('portfolios')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error

    const portfolios: Portfolio[] = data.map(p => ({
      id: p.id,
      userId: p.user_id,
      name: p.name,
      totalAmount: p.total_amount,
      allocations: p.allocations,
      createdAt: p.created_at,
      updatedAt: p.updated_at,
    }))

    set({ portfolios })
  },

  fetchAvailableStocks: async () => {
    const { data, error } = await supabase
      .from('stocks')
      .select('*')
      .eq('is_sharia_compliant', true)
      .order('symbol')

    if (error) throw error

    const stocks: Stock[] = data.map(s => ({
      id: s.id,
      symbol: s.symbol,
      name: s.name,
      sector: s.sector,
      isShariCompliant: s.is_sharia_compliant,
      shariaKingSince: s.sharia_king_since || undefined,
      debtRatio: s.debt_ratio,
      nonHalalRevenueRatio: s.non_halal_revenue_ratio,
      liquidityRatio: s.liquidity_ratio,
      lastChecked: s.last_checked,
      createdAt: s.created_at,
      updatedAt: s.updated_at,
    }))

    set({ availableStocks: stocks })
  },

  updateStockCompliance: async () => {
    // Cette fonction sera appelée quotidiennement pour vérifier la conformité
    // Pour l'instant, c'est un placeholder
    console.log('Updating stock compliance...')
  },
}))

// Fonctions utilitaires pour la répartition
function distributeConservative(stocks: any[], amount: number) {
  // Répartition conservatrice : 60% sur les 3 premiers, 40% sur les autres
  const allocations = []
  const topStocks = stocks.slice(0, 3)
  const otherStocks = stocks.slice(3, 8)
  
  topStocks.forEach((stock, index) => {
    const percentage = index === 0 ? 25 : 17.5 // 25%, 17.5%, 17.5%
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: Math.round((amount * percentage) / 100),
      isShariKing: stock.sharia_king_since !== null
    })
  })
  
  otherStocks.forEach(stock => {
    const percentage = 8 // 8% chacun
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: Math.round((amount * percentage) / 100),
      isShariKing: stock.sharia_king_since !== null
    })
  })
  
  return allocations
}

function distributeModerate(stocks: any[], amount: number) {
  // Répartition équilibrée
  const basePercentage = 100 / stocks.length
  return stocks.map(stock => ({
    symbol: stock.symbol,
    name: stock.name,
    percentage: Math.round(basePercentage * 10) / 10,
    amount: Math.round((amount * basePercentage) / 100),
    isShariKing: stock.sharia_king_since !== null
  }))
}

function distributeAggressive(stocks: any[], amount: number) {
  // Répartition plus diversifiée avec des poids variables
  const weights = stocks.map((_, index) => Math.max(1, 15 - index))
  const totalWeight = weights.reduce((sum, w) => sum + w, 0)
  
  return stocks.map((stock, index) => {
    const percentage = Math.round((weights[index] / totalWeight) * 100 * 10) / 10
    return {
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: Math.round((amount * percentage) / 100),
      isShariKing: stock.sharia_king_since !== null
    }
  })
}

function getRiskScore(riskTolerance: string): number {
  switch (riskTolerance) {
    case 'conservative': return 3
    case 'moderate': return 5
    case 'aggressive': return 8
    default: return 5
  }
}

function getExpectedReturn(riskTolerance: string): number {
  switch (riskTolerance) {
    case 'conservative': return 6.5
    case 'moderate': return 8.2
    case 'aggressive': return 10.8
    default: return 8.2
  }
}

function generateReasoning(riskTolerance: string, purityLevel: number, stockCount: number): string {
  let reasoning = `Portefeuille ${riskTolerance === 'conservative' ? 'conservateur' : riskTolerance === 'moderate' ? 'équilibré' : 'dynamique'} `
  reasoning += `avec ${stockCount} actions sélectionnées selon vos critères de pureté (${purityLevel}%). `
  
  if (purityLevel >= 99) {
    reasoning += 'Priorité donnée aux "Sharia Kings" pour une stabilité maximale.'
  } else if (purityLevel >= 97) {
    reasoning += 'Critères stricts appliqués pour une conformité renforcée.'
  } else {
    reasoning += 'Critères AAOIFI standards respectés.'
  }
  
  return reasoning
}
