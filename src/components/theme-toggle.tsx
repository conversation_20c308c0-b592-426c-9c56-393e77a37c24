"use client"

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import { useEffect, useState } from "react"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const cycleTheme = () => {
    if (theme === "light") {
      setTheme("dark")
    } else if (theme === "dark") {
      setTheme("system")
    } else {
      setTheme("light")
    }
  }

  const getIcon = () => {
    if (!mounted) return <Sun className="h-4 w-4" />

    if (theme === "light") {
      return <Sun className="h-4 w-4" />
    } else if (theme === "dark") {
      return <Moon className="h-4 w-4" />
    } else {
      return <Monitor className="h-4 w-4" />
    }
  }

  const getTooltip = () => {
    if (!mounted) return "Changer le thème"

    if (theme === "light") {
      return "Mode clair (cliquer pour mode sombre)"
    } else if (theme === "dark") {
      return "Mode sombre (cliquer pour mode système)"
    } else {
      return "Mode système (cliquer pour mode clair)"
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={cycleTheme}
      className="h-9 w-9"
      title={getTooltip()}
    >
      {getIcon()}
      <span className="sr-only">Changer le thème</span>
    </Button>
  )
}
