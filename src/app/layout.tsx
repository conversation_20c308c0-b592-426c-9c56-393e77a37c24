import type React from "react"
import type { Metada<PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import { Analytics } from "@vercel/analytics/next"
import { Suspense } from "react"
import { ThemeProvider } from "next-themes"
import { NotificationProvider } from "@/components/notifications/NotificationProvider"
import "./globals.css"

export const metadata: Metadata = {
  title: "Halal Invest Advisor - Sharia-Compliant Investment Platform",
  description:
    "Discover personalized halal investment portfolios that respect Islamic principles. Get Sharia-compliant investment advice with ethical filters and real-time compliance monitoring.",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem={true}
          enableColorScheme={true}
          disableTransitionOnChange={false}
          storageKey="halal-invest-theme"
        >
          <NotificationProvider>
            <Suspense fallback={null}>{children}</Suspense>
          </NotificationProvider>
        </ThemeProvider>
        <Analytics />
      </body>
    </html>
  )
}
