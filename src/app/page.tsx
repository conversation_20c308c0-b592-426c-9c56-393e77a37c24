'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import { AnimatedBackground } from "@/components/animated-background"
import { ShaderBackground } from "@/components/shader-background"
import { IslamicFloatingElements } from "@/components/floating-elements"
import { ChartsGrid } from "@/components/animated-charts"
import { EnhancedWaves } from "@/components/enhanced-waves"
import { MediumGreenTintedBackground } from "@/components/tinted-section-background"
import Link from "next/link"
import { motion } from "framer-motion"
import {
  TrendingUp,
  Shield,
  Users,
  BarChart3,
  CheckCircle,
  Star,
  ArrowRight,
  Zap,
  Globe,
  Award
} from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Animations de fond */}
      <EnhancedWaves variant="aurora" intensity="medium" />
      <AnimatedBackground variant="particles" />
      <ShaderBackground variant="aurora" intensity="low" />
      <IslamicFloatingElements />

      {/* Header */}
      <header className="border-b border-border bg-card/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <motion.div
            className="flex items-center space-x-2"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center glow-primary">
              <span className="text-primary-foreground text-lg">🕌</span>
            </div>
            <span className="text-xl font-bold text-foreground">Halal Invest</span>
          </motion.div>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="#features" className="text-muted-foreground hover:text-foreground transition-colors">
              Fonctionnalités
            </Link>
            <Link href="#sharia" className="text-muted-foreground hover:text-foreground transition-colors">
              Conformité Sharia
            </Link>
            <Link href="#analytics" className="text-muted-foreground hover:text-foreground transition-colors">
              Analytics
            </Link>
          </nav>

          <motion.div
            className="flex items-center space-x-4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <ThemeToggle />
            <Link href="/auth/login" className="text-muted-foreground hover:text-foreground transition-colors">
              Se connecter
            </Link>
            <Link href="/auth/register">
              <Button size="sm" className="glow-primary">
                S'inscrire
              </Button>
            </Link>
          </motion.div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 relative z-10">
        <div className="container mx-auto text-center max-w-5xl">
          <motion.div
            className="mb-6 inline-block bg-secondary text-secondary-foreground border border-border px-4 py-2 rounded-full text-sm font-medium"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            🕌 Plateforme d'Investissement Conforme à la Sharia
          </motion.div>

          <motion.h1
            className="text-4xl md:text-7xl font-bold text-foreground mb-6 leading-tight"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Investissez selon les{" "}
            <span className="text-primary text-emerald-600">
              Principes Islamiques
            </span>
          </motion.h1>

          <motion.p
            className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Découvrez des portefeuilles d'investissement halal personnalisés qui respectent les valeurs islamiques.
            Obtenez des conseils conformes à la Sharia avec des filtres éthiques automatisés et une technologie de pointe.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Link href="/auth/register">
              <Button size="lg" className="glow-primary group">
                Commencer maintenant
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button size="lg" variant="outline" className="border-glow">
                Se connecter
              </Button>
            </Link>
          </motion.div>

          {/* Stats */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">15,000+</div>
              <div className="text-muted-foreground">Investisseurs Musulmans</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">1,200+</div>
              <div className="text-muted-foreground">Actions Halal Vérifiées</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">99.9%</div>
              <div className="text-muted-foreground">Conformité Sharia</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">24/7</div>
              <div className="text-muted-foreground">Surveillance Continue</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features */}
      <section id="features" className="py-20 px-4 relative">
        {/* Background avec teinte verte plus visible */}
        <MediumGreenTintedBackground />
        <div className="container mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Pourquoi choisir Halal Invest ?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Une plateforme complète qui allie technologie moderne et principes islamiques
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <motion.div
              className="bg-card p-8 rounded-xl shadow-sm border border-border hover:shadow-lg transition-all duration-300 group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-primary text-3xl mb-6 transition-transform">
                <Shield className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Conformité Sharia Garantie</h3>
              <p className="text-muted-foreground leading-relaxed">
                Filtrage automatique selon les critères AAOIFI et ISRA. Exclusion des secteurs interdits
                et vérification continue des ratios financiers.
              </p>
            </motion.div>

            <motion.div
              className="bg-card p-8 rounded-xl shadow-sm border border-border hover:shadow-lg transition-all duration-300 group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="text-primary text-3xl mb-6 transition-transform">
                <Award className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Sharia Kings</h3>
              <p className="text-muted-foreground leading-relaxed">
                Découvrez les entreprises conformes à la Sharia depuis plus de 10 ans consécutifs,
                pour une stabilité et une fiabilité maximales.
              </p>
            </motion.div>

            <motion.div
              className="bg-card p-8 rounded-xl shadow-sm border border-border hover:shadow-lg transition-all duration-300 group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="text-primary text-3xl mb-6 transition-transform">
                <BarChart3 className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">IA Personnalisée</h3>
              <p className="text-muted-foreground leading-relaxed">
                Portefeuilles adaptés par IA à votre profil de risque, budget et préférences éthiques.
                Contrôlez votre niveau de pureté Sharia.
              </p>
            </motion.div>
          </div>

          {/* Fonctionnalités avancées */}
          <div className="grid md:grid-cols-2 gap-8">
            <motion.div
              className="bg-card p-8 rounded-xl shadow-sm border border-border"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center mb-4">
                <Zap className="w-6 h-6 text-primary mr-3" />
                <h3 className="text-xl font-semibold text-foreground">Surveillance en Temps Réel</h3>
              </div>
              <p className="text-muted-foreground mb-4">
                Monitoring 24/7 de la conformité Sharia de vos investissements avec alertes automatiques.
              </p>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-primary mr-2" /> Alertes de non-conformité</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-primary mr-2" /> Rapports détaillés</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-primary mr-2" /> Recommandations automatiques</li>
              </ul>
            </motion.div>

            <motion.div
              className="bg-card p-8 rounded-xl shadow-sm border border-border"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center mb-4">
                <Globe className="w-6 h-6 text-primary mr-3" />
                <h3 className="text-xl font-semibold text-foreground">Marchés Globaux</h3>
              </div>
              <p className="text-muted-foreground mb-4">
                Accès aux marchés internationaux avec filtrage Sharia unifié selon les standards mondiaux.
              </p>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-primary mr-2" /> NYSE, NASDAQ, LSE</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-primary mr-2" /> Marchés émergents</li>
                <li className="flex items-center"><CheckCircle className="w-4 h-4 text-primary mr-2" /> Sukuk et ETF Sharia</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Section Conformité Sharia */}
      <section id="sharia" className="py-20 px-4 relative">
        {/* Fond sombre pour contraster avec la section précédente */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-background"></div>

        <div className="container mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Conformité Sharia Certifiée
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Filtrage automatique selon les standards internationaux avec surveillance continue
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold text-foreground mb-6">Critères de Filtrage</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-xs">✕</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Secteurs Interdits</h4>
                    <p className="text-muted-foreground text-sm">Alcool, tabac, gambling, banques conventionnelles, armement</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-xs">%</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Ratios Financiers</h4>
                    <p className="text-muted-foreground text-sm">Dette/Capitalisation &lt; 33%, Liquidités/Capitalisation &lt; 33%</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-card/80 backdrop-blur-sm p-8 rounded-xl border border-border"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Standards Internationaux</h3>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="p-4 bg-primary/10 rounded-lg">
                  <div className="text-2xl font-bold text-primary">AAOIFI</div>
                  <p className="text-xs text-muted-foreground">Standards Comptables</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <div className="text-2xl font-bold text-primary">ISRA</div>
                  <p className="text-xs text-muted-foreground">Recherche Islamique</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <div className="text-2xl font-bold text-primary">IFSB</div>
                  <p className="text-xs text-muted-foreground">Services Financiers</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <div className="text-2xl font-bold text-primary">OIC</div>
                  <p className="text-xs text-muted-foreground">Coopération Islamique</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Section Analytics et Performance */}
      <section id="analytics" className="py-20 px-4 relative">
        {/* Fond avec overlay subtil */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/40 via-background/80 to-muted/40"></div>
        <div className="container mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Analytics & Performance
            </h2>
            <p className="text-xl text-muted-foreground">
              Visualisez la performance de vos investissements halal en temps réel
            </p>
          </motion.div>

          {/* Graphiques animés */}
          <ChartsGrid className="mb-16" />

          {/* Métriques de performance */}
          <motion.div
            className="grid md:grid-cols-4 gap-6 mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border text-center">
              <div className="text-3xl font-bold text-primary mb-2">+12.4%</div>
              <p className="text-sm text-muted-foreground">Performance Annuelle</p>
            </div>
            <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border text-center">
              <div className="text-3xl font-bold text-primary mb-2">98.7%</div>
              <p className="text-sm text-muted-foreground">Précision Screening</p>
            </div>
            <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border text-center">
              <div className="text-3xl font-bold text-primary mb-2">1,247</div>
              <p className="text-sm text-muted-foreground">Actions Analysées</p>
            </div>
            <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border text-center">
              <div className="text-3xl font-bold text-primary mb-2">24/7</div>
              <p className="text-sm text-muted-foreground">Monitoring Continu</p>
            </div>
          </motion.div>

          {/* Call to action */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="bg-card/80 backdrop-blur-sm p-8 rounded-xl border border-border max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Prêt à commencer votre parcours d'investissement halal ?
              </h3>
              <p className="text-muted-foreground mb-6">
                Rejoignez des milliers d'investisseurs musulmans qui font confiance à notre plateforme
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/register">
                  <Button size="lg" className="glow-primary">
                    Commencer gratuitement
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="#features">
                  <Button size="lg" variant="outline" className="border-glow">
                    En savoir plus
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-border relative">
        {/* Fond sombre pour le footer */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/60 via-background to-muted/40"></div>
        <div className="container mx-auto relative z-10">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground text-lg">🕌</span>
                </div>
                <span className="text-xl font-bold text-foreground">Halal Invest</span>
              </div>
              <p className="text-muted-foreground text-sm">
                La première plateforme d'investissement conforme à la Sharia avec IA intégrée.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-4">Produits</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="#" className="hover:text-foreground transition-colors">Screening Sharia</Link></li>
                <li><Link href="#" className="hover:text-foreground transition-colors">Portefeuilles IA</Link></li>
                <li><Link href="#" className="hover:text-foreground transition-colors">API Développeurs</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-4">Ressources</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="#" className="hover:text-foreground transition-colors">Documentation</Link></li>
                <li><Link href="#" className="hover:text-foreground transition-colors">Blog</Link></li>
                <li><Link href="#" className="hover:text-foreground transition-colors">Support</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-4">Légal</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="#" className="hover:text-foreground transition-colors">Conditions d'utilisation</Link></li>
                <li><Link href="#" className="hover:text-foreground transition-colors">Politique de confidentialité</Link></li>
                <li><Link href="#" className="hover:text-foreground transition-colors">Mentions légales</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>&copy; 2024 Halal Invest. Tous droits réservés. Certifié conforme à la Sharia.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
