// Types pour l'application Halal Invest

export interface UserProfile {
  id: string
  email: string
  fullName?: string
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  investmentHorizon: 'short' | 'medium' | 'long'
  monthlyBudget: number
  shariaPurityLevel: number // 95-100
  boycottPreferences: string[]

  // Nouvelles données d'onboarding
  hasInvestedBefore: boolean
  isCurrentlyInvesting: boolean
  currentInvestmentAmount?: number
  monthlyIncome?: number
  monthlySavings?: number
  investmentGoals: string[]
  age?: number
  profession?: string
  onboardingCompleted: boolean

  createdAt: string
  updatedAt: string
}

export interface Stock {
  id: string
  symbol: string
  name: string
  sector: string
  isShariCompliant: boolean
  shariaKingSince?: string // Date depuis laquelle c'est un Sharia King
  debtRatio: number
  nonHalalRevenueRatio: number
  liquidityRatio: number
  currentPrice?: number
  lastChecked: string
  createdAt: string
  updatedAt: string
}

export interface PortfolioAllocation {
  symbol: string
  name: string
  percentage: number
  amount: number
  isShariKing?: boolean
}

export interface Portfolio {
  id: string
  userId: string
  name: string
  totalAmount: number
  allocations: PortfolioAllocation[]
  createdAt: string
  updatedAt: string
}

export interface Notification {
  id: string
  userId: string
  type: 'sharia_compliance_change' | 'portfolio_update' | 'new_recommendation'
  title: string
  message: string
  isRead: boolean
  createdAt: string
}

export interface OnboardingQuestion {
  id: string
  type: 'single-choice' | 'multiple-choice' | 'slider' | 'input' | 'currency'
  title: string
  subtitle?: string
  question: string
  options?: {
    value: string | number
    label: string
    description?: string
    icon?: string
  }[]
  min?: number
  max?: number
  step?: number
  required?: boolean
  validation?: (value: any) => boolean | string
}

export interface OnboardingStep {
  id: string
  title: string
  description: string
  icon: string
  questions: OnboardingQuestion[]
  progress: number
}

export interface OnboardingData {
  // Profil personnel
  age?: number
  profession?: string
  monthlyIncome?: number
  monthlySavings?: number

  // Expérience d'investissement
  hasInvestedBefore: boolean
  isCurrentlyInvesting: boolean
  currentInvestmentAmount?: number
  investmentExperience: 'beginner' | 'intermediate' | 'advanced'

  // Objectifs et préférences
  investmentGoals: string[]
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  investmentHorizon: 'short' | 'medium' | 'long'
  monthlyBudget: number

  // Préférences Sharia
  shariaPurityLevel: number
  boycottPreferences: string[]
  shariaKnowledge: 'beginner' | 'intermediate' | 'advanced'
}

export interface ShariaScreeningCriteria {
  maxDebtRatio: number // 33%
  maxNonHalalRevenue: number // 5%
  maxLiquidityRatio: number // 33%
  excludedSectors: string[]
}

export interface FinancialData {
  symbol: string
  price: number
  marketCap: number
  totalDebt: number
  totalRevenue: number
  nonHalalRevenue: number
  cashAndEquivalents: number
  lastUpdated: string
}

export interface PortfolioRecommendation {
  allocations: PortfolioAllocation[]
  totalAmount: number
  riskScore: number
  shariaCompliance: number
  expectedReturn: number
  reasoning: string
}

// Constantes pour les secteurs exclus (Sharia)
export const EXCLUDED_SECTORS = [
  'Alcohol',
  'Tobacco',
  'Gambling',
  'Conventional Banking',
  'Conventional Insurance',
  'Weapons & Defense',
  'Adult Entertainment',
  'Pork Products'
] as const

// Constantes pour les entreprises couramment boycottées
export const COMMON_BOYCOTT_COMPANIES = [
  'Coca-Cola',
  'PepsiCo',
  'Nestlé',
  'McDonald\'s',
  'Starbucks',
  'KFC',
  'Burger King',
  'Pizza Hut'
] as const

export type ExcludedSector = typeof EXCLUDED_SECTORS[number]
export type BoycottCompany = typeof COMMON_BOYCOTT_COMPANIES[number]
