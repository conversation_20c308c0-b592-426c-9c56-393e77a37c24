{"name": "halal-invest", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "check-setup": "node scripts/check-setup.js"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.57.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/js-cookie": "^3.0.6", "@vercel/analytics": "^1.5.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.2", "framer-motion": "^12.23.12", "geist": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.542.0", "next": "15.5.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.8", "typescript": "^5"}}