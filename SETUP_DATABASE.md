# 🗄️ Configuration de la base de données

## ⚠️ Configuration manuelle requise

La configuration automatique de la base de données ne fonctionne pas de manière fiable. **Il est nécessaire d'exécuter manuellement le script SQL dans Supabase Dashboard.**

## 📋 Étapes de configuration

### 1. Accéder à Supabase Dashboard

1. <PERSON><PERSON> sur https://supabase.com/dashboard
2. Sélectionner votre projet
3. Cliquer sur **"SQL Editor"** dans la sidebar gauche

### 2. Exécuter le script SQL

1. **Copier** tout le contenu du fichier `supabase-schema.sql`
2. **Coller** dans l'éditeur SQL de Supabase
3. **Cliquer sur "Run"** pour exécuter le script

### 3. Vérifier la création des tables

Après exécution, vous devriez voir :
- ✅ Tables créées : `profiles`, `stocks`, `portfolios`, `notifications`
- ✅ Triggers configurés pour `updated_at`
- ✅ Politiques RLS activées
- ✅ Fonction `handle_new_user` créée
- ✅ Données d'exemple insérées dans `stocks`

## 🔧 Résolution des problèmes

### Erreur 406 (Not Acceptable)
Si vous voyez des erreurs 406 lors de l'accès aux profils :

**🚨 Solution rapide** : Exécuter le script `fix-rls-policies.sql` dans Supabase Dashboard

1. **Diagnostic** :
   - Exécuter `diagnostic-rls.sql` pour identifier le problème
   - Vérifier les logs dans Supabase Dashboard → Logs

2. **Correction** :
   - Exécuter `fix-rls-policies.sql` pour corriger les politiques RLS
   - Redémarrer l'application Next.js

3. **Vérification** :
   ```sql
   -- Vérifier que les politiques existent
   SELECT * FROM pg_policies WHERE tablename = 'profiles';

   -- Tester l'accès
   SELECT COUNT(*) FROM profiles;
   ```

### Tables non trouvées
Si l'application indique que les tables n'existent pas :

1. **Vérifier dans Supabase Dashboard** → Table Editor
2. **Réexécuter le script SQL complet**
3. **Vérifier les permissions** de votre clé API

### Trigger non fonctionnel
Si les profils ne se créent pas automatiquement :

1. **Vérifier le trigger** :
   ```sql
   SELECT * FROM information_schema.triggers 
   WHERE trigger_name = 'on_auth_user_created';
   ```

2. **Recréer le trigger** :
   ```sql
   DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
   CREATE TRIGGER on_auth_user_created
     AFTER INSERT ON auth.users
     FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
   ```

## 🧪 Test de la configuration

### Via l'application
1. Lancer `npm run dev`
2. Aller sur http://localhost:3001/dashboard
3. Cliquer sur "Vérifier la configuration" (mode développement)
4. Vérifier la console pour les résultats

### Via script
```bash
npm run check-setup
```

### Manuellement dans Supabase
```sql
-- Tester l'accès aux tables
SELECT COUNT(*) FROM profiles;
SELECT COUNT(*) FROM stocks;
SELECT COUNT(*) FROM portfolios;
SELECT COUNT(*) FROM notifications;

-- Vérifier les politiques RLS
SELECT * FROM pg_policies WHERE tablename IN ('profiles', 'stocks', 'portfolios', 'notifications');
```

## 📝 Notes importantes

- **Ne pas** essayer de créer les tables via l'API REST
- **Toujours** utiliser l'éditeur SQL de Supabase Dashboard
- **Vérifier** que RLS est activé sur toutes les tables
- **Tester** la création de profil après configuration

## 🆘 Support

Si vous rencontrez des problèmes :

1. **Vérifier les logs** dans la console du navigateur
2. **Consulter** les logs Supabase Dashboard → Logs
3. **Réexécuter** le script SQL complet
4. **Contacter** l'équipe de développement avec les détails de l'erreur

---

**⚠️ Rappel** : La configuration automatique a été supprimée car elle ne fonctionnait pas de manière fiable. La méthode manuelle est la seule méthode supportée.
